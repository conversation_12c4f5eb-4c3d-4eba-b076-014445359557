---
type: "always_apply"
---

# Shared Library Usage (`shared/lib`)

To maintain consistency and support internationalization (i18n), all Next.js components and hooks must be imported from the `shared/lib` module.

- **Purpose**: Centralizes custom Next.js implementations for project-wide consistency and i18n support.
- **Import Rule**: Always import Next.js components and hooks from `shared/lib`.
  - **Correct**: `import { Link, useRouter } from "shared/lib";`
  - **Incorrect**: `import { Link } from "next/link";`
- **Page Components**: Next.js 15 page props (`params`, `searchParams`) are Promises and require `await` for access.
- **Internationalization (i18n)**:
  - **Navigation**: Utilize `next-intl` integrated navigation components.
  - **Server Components**:
    - Translations: Use `getTranslations` from `next-intl/server`.
    - Locale: Use `getLocale` from `shared/lib`.
  - **Client Components**:
    - Translations: Use `useTranslations` from `next-intl`.
    - Navigation/Locale: Use `useRouter`, `usePathname`, `Link`, and `useLocale` from `shared/lib`.
