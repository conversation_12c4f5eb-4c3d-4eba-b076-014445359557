---
type: "agent_requested"
description: "Add anchor tags to section comments."
---

```sql
-- section SCHEMA
-- sql statements
-- !section
-- section ENUMS

-- anchor KYC_STATUS
-- sql statements
-- anchor AVAILABILITY_TYPE
-- sql statements
-- !section

-- section TRIGGER FUNCTIONS
-- anchor check_availability_overlap
-- sql statements
-- anchor check_ban_constraints
-- sql statements
-- !section
```

Do not create the same anchor and section comments more than once.
Always try to group them together in the same section.
