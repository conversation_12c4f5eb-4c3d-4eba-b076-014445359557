---
type: "agent_requested"
description: "Commenting rules for Supabase"
---

# Comments

## Complex Logic

Add comments for complex logic, non-obvious constraints, functions, and triggers.

## Security

Explain security models for security-related functions.

## Changes

Do not create comments about your changes in the code. I can see them using git diff. For example this one

```sql
  SELECT
  app_access.define_role_capability (
  'provider',
  ARRAY[
    'provider.review.submit',
    'provider.status.edit' -- New capability <- this is not a comment I need
  ]
)
;
```
