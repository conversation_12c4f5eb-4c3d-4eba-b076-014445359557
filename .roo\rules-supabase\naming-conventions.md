---
type: "agent_requested"
description: "Naming conventions for db objects"
---

# Naming Conventions

Always use the following naming conventions for SQL objects:

- Schemas: `snake_case`, prefix application schemas with `app_` (e.g., `app_account`).
- Tables: Singular `snake_case` (e.g., `profile`).
- Columns: Descriptive `snake_case` (e.g., `user_id`).
- RLS Policies: `table_operation_scope` (e.g., `profile_select_all`, `kyc_insert_own`).
- Functions: Descriptive `snake_case` (e.g., `is_admin`).
- Function Variables: Prefix with `v_` (e.g., `v_user_id`).
- Enum Types: Uppercase names, lowercase values, singular (e.g., `CREATE TYPE GENDER AS ENUM('male', 'female')`). Use for fixed sets of values. Prefer enums over tables for fixed sets.
- Domain Types: Uppercase names (e.g., `CREATE DOMAIN EMAIL AS TEXT CHECK (...)`).
- Indexes: `idx_tablename_columnname(s)` (e.g., `idx_profile_user_id`, `idx_order_client_id_provider_id`).
