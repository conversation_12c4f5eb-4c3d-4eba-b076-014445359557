# Platform Features and Admin Dashboard Requirements - Development Status

This document compares the features outlined in `docs/tasks/01-features.md` with the current `supabase/migrations` database schema to assess development progress.

## User Management

- **User Accounts:**
  - [x] Clients and Providers have different account types.
  - [x] Providers have detailed profiles (skills, interests, availability, services, pricing, voice preview) - _Profile, selected_activity, service, and availability tables support this._
  - [x] Clients have basic profiles and activity history.
  - [x] Real names are collected for clients for financial actions - _Supported by `app_account.kyc`._
  - **Admin:**
    - [x] View, edit, and delete user profiles - _Supported by RLS policies and admin roles._
    - [x] Manage Provider profile details - _Supported by RLS policies and admin roles._
    - [ ] See client transaction history - _Transaction tables exist, but explicit "client transaction history" view for admin is not detailed in migrations._
    - [ ] Ban users - _Implicitly possible by deleting user accounts/roles, but no explicit "ban" mechanism in migrations._
    - [x] Manage user verification status - _Supported by `app_account.kyc` status and admin RLS._
- **Verification:**
  - [x] Optional or required steps to verify users.
  - [x] Collecting real name/surname for financial transactions is an initial step - _Supported by `app_account.kyc`._
  - **Admin:**
    - [x] Review and manage user verification status - _Supported by `app_account.kyc` status and admin RLS._

## Service Management

- **Service Listings:**
  - [x] Providers list services they offer (gaming, coding help, etc.) - _Supported by `app_catalog.activity`, `app_catalog.category`, `app_provider.selected_activity`, `app_provider.service`._
  - [x] Listings include descriptions, Soda price, and duration - _Supported by `app_provider.service` (soda_amount) and `app_catalog.pricing_unit`._
  - [x] Predefined activities and categories are used - _Supported by `app_catalog.activity` and `app_catalog.category`._
  - **Admin:**
    - [x] View, edit, hide, and delete service listings - _Supported by RLS policies and admin roles._
    - [x] Manage predefined activities and categories - _Supported by RLS policies and admin roles._
- **Search and Discovery:**
  - [x] Clients can search for Providers using filters (gender, activity, category, language, availability) - _Underlying data structures (`app_account.profile`, `app_catalog.activity`, `app_catalog.category`, `app_account.locale`, `app_account.availability`) are present._
  - [ ] Results can be sorted (rating, price).
  - [ ] Featured Providers can be highlighted.
- **Booking and Scheduling:**
  - [x] Clients book services based on Provider availability - _Supported by `app_account.availability` and `app_transaction.order`._
  - [x] The system includes a calendar - _Availability data is present, but calendar UI is application-side._
  - [x] Providers can set custom booking times for services - _Supported by `app_account.availability`._
  - **Admin:**
    - [x] View booking details - _Supported by `app_transaction.order` and admin RLS._
    - [x] Potentially manage Provider booking settings - _Supported by `app_account.availability` and admin RLS._

## Communication

- **Messaging:**
  - [ ] Clients and Providers can send text messages within the platform.
  - [ ] Messaging is limited to Client-Provider conversations.
  - **Admin:**
    - [ ] View messages for moderation.
    - [ ] Hide or delete message content.
- **Silent Mode:**
  - [ ] Users can mute notifications for specific chats.
- **Read Receipts:**
  - [ ] Users can see when messages are read.
- **Typing Indicators:**
  - [ ] Users see when others are typing.
- **Message Search:**
  - [ ] Users can search their conversation history.
- **Blocking:**
  - [ ] Users can block others to stop messages and hide conversations.
  - [ ] A list of blocked users is available.
  - **Admin:**
    - [ ] View and manage blocked user lists.

## Financial System (Soda & Caps)

- **Soda (Primary Currency):**
  - [x] Clients buy Soda with real money (1 Soda = 10 TRY) - _Supported by `app_transaction.currency`, `app_transaction.config`, `app_transaction.deposit`._
  - [x] Soda is used only for booking Provider services - _Supported by `app_transaction.order`._
  - [x] Client Soda balance is shown in their wallet - _Supported by `app_transaction.wallet`._
  - **Admin:**
    - [x] Process manual bank transfers for Soda purchases - _Supported by `app_transaction.deposit` (manual processing is external to DB)._
    - [x] View and manage Soda balances - _Supported by `app_transaction.wallet` and admin RLS._
    - [x] View Soda transaction logs - _Supported by `app_transaction.deposit`, `app_transaction.transfer`, `app_transaction.withdrawal`._
    - [x] Configure the Soda exchange rate - _Supported by `app_transaction.currency` and `app_transaction.config`._
- **Caps (Reward Currency):**
  - [x] Clients earn Caps by engaging with the platform (purchases, logins, events, tasks) - _`app_transaction.wallet` tracks Caps, but earning logic is application-side._
  - [x] All users have a Caps wallet - _Supported by `app_transaction.wallet`._
  - **Admin:**
    - [x] View and manage Caps balances - _Supported by `app_transaction.wallet` and admin RLS._
    - [ ] Manage how Caps are earned - _This logic is application-side and not directly in migrations._
- **Caps Marketplace:**
  - [ ] Clients use Caps to buy items like profile cosmetics, profile boosts, exclusive content, or event entries.
  - **Admin:**
    - [ ] Manage items in the Caps Store (add, edit, remove, set prices).
- **Withdrawals:**
  - [x] Providers can convert Soda earnings to TRY - _Supported by `app_transaction.withdrawal_request` and `app_transaction.withdrawal`._
  - [x] Initially, this is done via manual bank transfers - _Supported by the withdrawal process in migrations (manual processing is external to DB)._
  - [x] Basic transaction logs are kept - _Supported by `app_transaction.withdrawal`._
  - **Admin:**
    - [x] Process manual bank transfers for Provider withdrawals - _Supported by `app_transaction.withdrawal_request` status updates and admin RLS._
    - [x] View Soda transaction logs - _Supported by `app_transaction.withdrawal` and `app_dashboard.withdrawal` view._

## Engagement and Gamification

- **Daily Tasks:**
  - [ ] Clients complete tasks (log in, browse, review) to earn Caps.
  - [ ] Tasks and rewards vary. Some tasks are static, others are managed by admins.
  - **Admin:**
    - [ ] Manage daily tasks (add, edit, remove, set rewards).
- **Daily Login Bonuses:**
  - [ ] Clients get Caps for logging in daily.
  - [ ] Rewards increase with consecutive logins.
  - [ ] Clients must claim the bonus.
  - [ ] The streak resets after a set time or missed day.
  - **Admin:**
    - [ ] Configure login bonus streak duration and rewards.
- **Achievements and Badges:**
  - [ ] Clients and Providers earn awards for reaching milestones (first booking, 5-star review, etc.).
  - **Admin:**
    - [ ] Manage achievements and badges (define criteria, award manually).
- **Leaderboards:**
  - [ ] Show top Providers or active clients based on criteria like Caps earned or spent.
  - **Admin:**
    - [ ] Configure leaderboard criteria and display.
- **Events and Contests:**
  - [x] Platform-wide events where clients can earn extra rewards - _Supported by `app_wiki.event`._
  - [x] Initial types include content creation, time-based events, and task-based contests - _Supported by `app_wiki.event`._
  - **Admin:**
    - [x] Create and manage events and contests - _Supported by `app_wiki.event` and admin RLS._

## Safety and Moderation

- **Reporting System:**
  - [x] Clients report inappropriate behavior or content using predefined categories (harassment, spam, fraud, etc.) - _Supported by `app_support.ticket_category` and `app_support.ticket`._
  - **Admin:**
    - [x] Review reported content and behavior - _Supported by `app_support.ticket` and admin RLS._
- **Moderation Tools:**
  - [x] Admins review reports and take action (warnings, suspension, content removal) - _Admin roles and RLS policies allow for actions, but specific tables for warnings/suspensions are not explicit._
  - [x] Admins can ban users and hide/delete user content (profiles, listings, messages) - _Implicitly possible through RLS and data deletion/status updates._
  - **Admin:**
    - [x] Access moderation tools to issue warnings, suspend users, ban users, and remove content - _Admin RLS provides access, but specific "tools" are application-side._
- **Dispute Resolution:**
  - [x] A process for resolving issues between clients and Providers - _Supported by `app_transaction.order` status (`in_dispute`) and helper functions._
  - [x] Users provide information, and a moderator makes a decision - _Supported by `app_support.ticket` for information gathering, and admin functions for decisions._
  - **Admin:**
    - [x] Manage the dispute resolution workflow and make decisions - _Supported by admin RLS and helper functions._
- **Help/Support:**
  - [x] Documentation on dispute resolution, policies, guidelines, reporting, and consequences - _Supported by `app_wiki.document` and `app_wiki.rule`._
  - **Admin:**
    - [x] Manage the content of the Help/Support documentation - _Supported by `app_wiki.document`, `app_wiki.rule` and admin RLS._
- **Provider Reviews of Clients:**
  - [x] Providers can rate clients for behavior - _Supported by `app_provider.review`._
  - [x] These ratings are hidden from clients and shown to other Providers for caution - _RLS policies on `app_provider.review` would control visibility._
  - **Admin:**
    - [x] View Provider ratings of clients - _Supported by `app_provider.review` and admin RLS._

## Future Potential Features

- [ ] **Phone Verification:** Mandatory phone verification steps for all users.
- [ ] **Subscription Tiers:** Premium options with extra benefits.
- [ ] **Gift Giving:** Clients can send Soda or Caps as gifts.
- [ ] **Affiliate Program:** Reward users for referring others.
- [ ] **Voice/Video Calls:** Planned as a future feature.
- [ ] **Group Chats:** Planned as a future feature.
