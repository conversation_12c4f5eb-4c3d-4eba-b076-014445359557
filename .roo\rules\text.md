---
type: "always_apply"
---

# E-Senpai Text Rules & Localization Strategy Summary

This document outlines the text and localization guidelines for E-Senpai, focusing on brand identity, tone, formatting, localization strategy, email communication, error messages, implementation, and quality assurance.

## 1. Brand Identity

- **Name**: "E-Senpai" (with hyphen).

## 2. Tone of Voice

- **General**: Casual, friendly, concise, helpful, inclusive, playful.
- **Language-Specific**:
  - **English**: Contractions, direct, active voice, non-technical.
  - **Korean**: Polite informal (해요체), culturally adapted, shorter.
  - **Japanese**: Question marks, です/ます form, proper particles/structure, cultural context.
  - **Turkish**: Informal pronouns (sen), shorter sentences, adapted expressions, specific word order.

## 3. Text Formatting

- **Capitalization**: Title Case (headings/buttons), Sentence case (long text/errors), no ALL CAPS.
- **Punctuation**: Periods for sentences (not UI labels), sparing exclamation points, question marks for interrogatives, ellipses for loading, no semicolons.
- **Numbers/Units**: Numerals, space between number and unit (e.g., "5 minutes"), locale-appropriate currency.
- **Emoji/Special Characters**: Use sparingly, culturally appropriate, cross-font tested.

## 4. Localization Strategy

- **Supported Languages**: English (default), Korean, Japanese, Turkish.
- **Translation Keys**: Nested, descriptive names, no hardcoded text (ESLint enforced).

## 5. Email Communication

- **Subject Lines**: Under 50 characters, clear purpose, "E-Senpai", avoid spam triggers.
- **Content**: Warm greeting, concise instructions, highlight important info (e.g., OTP), expiration, friendly sign-off.

## 6. Error Messages

- **Principles**: Specific, guiding, helpful, non-blaming, concise, consistent terminology.
- **Common Types**: "InvalidInput", "NotFound", "Unauthorized", "ServiceError", "DatabaseError", "CaptchaError".

## 7. Quality Assurance

- **Validation**: Completeness, tone/meaning.
- **Manual Review**: Native speakers, all languages, date/time/number formatting.
